import * as THREE from 'three';

/**
 * 进程状态枚举
 */
export const ProcessState = {
  NEW: 'NEW',           // 新建
  READY: 'READY',       // 就绪
  RUNNING: 'RUNNING',   // 运行
  WAITING: 'WAITING',   // 等待
  TERMINATED: 'TERMINATED' // 终止
} as const;
export type ProcessState = typeof ProcessState[keyof typeof ProcessState];

/**
 * 进程优先级枚举
 */
export const ProcessPriority = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  REALTIME: 4
} as const;
export type ProcessPriority = typeof ProcessPriority[keyof typeof ProcessPriority];

/**
 * 进程接口定义
 */
export interface Process {
  id: number;
  name: string;
  state: ProcessState;
  priority: ProcessPriority;
  cpuTime: number;
  waitTime: number;
  burstTime: number;
  mesh?: THREE.Mesh;
  infoBox?: THREE.Group;
}

/**
 * 进程调度器类
 * 实现进程调度的3D可视化
 */
export class ProcessScheduler {
  private scene: THREE.Scene;
  private processes: Process[] = [];
  private cpuCore: THREE.Mesh;
  private readyQueue: THREE.Group;
  private currentRunningProcess: Process | null = null;
  private schedulingAlgorithm: 'FCFS' | 'SJF' | 'RR' | 'PRIORITY' = 'RR';
  private timeQuantum: number = 2;
  private clock: number = 0;

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.cpuCore = this.createCPUCore();
    this.readyQueue = this.createReadyQueue();
    this.createWaitingArea();
    
    this.initializeProcesses();
  }

  /**
   * 创建CPU核心的3D模型
   */
  private createCPUCore(): THREE.Mesh {
    const geometry = new THREE.BoxGeometry(4, 4, 4);
    const material = new THREE.MeshPhongMaterial({
      color: 0x64b5f6,
      emissive: 0x2196f3,
      emissiveIntensity: 0.3
    });
    
    const cpu = new THREE.Mesh(geometry, material);
    cpu.position.set(0, 2, 0);
    cpu.castShadow = true;
    cpu.receiveShadow = true;
    
    // 添加CPU边框
    const edges = new THREE.EdgesGeometry(geometry);
    const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffffff, linewidth: 2 });
    const wireframe = new THREE.LineSegments(edges, lineMaterial);
    cpu.add(wireframe);
    
    // 添加CPU标签
    this.scene.add(cpu);
    
    return cpu;
  }

  /**
   * 创建就绪队列区域
   */
  private createReadyQueue(): THREE.Group {
    const group = new THREE.Group();
    group.position.set(-15, 0, 0);
    
    // 创建队列平台
    const platformGeometry = new THREE.BoxGeometry(10, 0.5, 20);
    const platformMaterial = new THREE.MeshPhongMaterial({
      color: 0x4caf50,
      transparent: true,
      opacity: 0.3
    });
    const platform = new THREE.Mesh(platformGeometry, platformMaterial);
    platform.position.y = -0.25;
    group.add(platform);
    
    this.scene.add(group);
    return group;
  }

  /**
   * 创建等待区域
   */
  private createWaitingArea(): THREE.Group {
    const group = new THREE.Group();
    group.position.set(15, 0, 0);
    
    // 创建等待区平台
    const platformGeometry = new THREE.BoxGeometry(10, 0.5, 20);
    const platformMaterial = new THREE.MeshPhongMaterial({
      color: 0xffa726,
      transparent: true,
      opacity: 0.3
    });
    const platform = new THREE.Mesh(platformGeometry, platformMaterial);
    platform.position.y = -0.25;
    group.add(platform);
    
    this.scene.add(group);
    return group;
  }

  /**
   * 初始化进程
   */
  private initializeProcesses(): void {
    const processNames = ['Chrome', 'VSCode', 'System', 'Discord', 'Spotify'];
    const priorities = [ProcessPriority.HIGH, ProcessPriority.NORMAL, ProcessPriority.LOW, ProcessPriority.NORMAL, ProcessPriority.LOW];
    
    for (let i = 0; i < 5; i++) {
      const process: Process = {
        id: i + 1,
        name: processNames[i],
        state: ProcessState.READY,
        priority: priorities[i],
        cpuTime: 0,
        waitTime: 0,
        burstTime: Math.floor(Math.random() * 5) + 3
      };
      
      process.mesh = this.createProcessMesh(process);
      this.processes.push(process);
      this.positionProcessInQueue(process, i);
    }
  }

  /**
   * 创建进程的3D模型
   */
  private createProcessMesh(process: Process): THREE.Mesh {
    const size = 1 + process.priority * 0.3;
    const geometry = new THREE.SphereGeometry(size, 32, 16);
    
    // 根据优先级设置颜色
    const colors = {
      [ProcessPriority.LOW]: 0x9e9e9e,
      [ProcessPriority.NORMAL]: 0x2196f3,
      [ProcessPriority.HIGH]: 0xff9800,
      [ProcessPriority.REALTIME]: 0xf44336
    };
    
    const material = new THREE.MeshPhongMaterial({
      color: colors[process.priority],
      emissive: colors[process.priority],
      emissiveIntensity: 0.2
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    
    // 添加进程信息标签
    this.addProcessLabel(mesh, process);
    
    this.scene.add(mesh);
    return mesh;
  }

  /**
   * 添加进程标签
   */
  private addProcessLabel(mesh: THREE.Mesh, process: Process): void {
    // 这里可以使用CSS2DRenderer来添加HTML标签
    // 简化起见，我们用一个小的平面来表示
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 64;
    const context = canvas.getContext('2d')!;
    
    context.fillStyle = 'rgba(0, 0, 0, 0.7)';
    context.fillRect(0, 0, 256, 64);
    
    context.fillStyle = 'white';
    context.font = '24px Arial';
    context.fillText(process.name, 10, 40);
    
    const texture = new THREE.CanvasTexture(canvas);
    const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(4, 1, 1);
    sprite.position.y = 2;
    
    mesh.add(sprite);
  }

  /**
   * 将进程放置在就绪队列中
   */
  private positionProcessInQueue(process: Process, index: number): void {
    if (process.mesh) {
      process.mesh.position.set(
        this.readyQueue.position.x,
        2,
        -8 + index * 4
      );
    }
  }

  /**
   * 执行调度算法
   */
  public schedule(): void {
    switch (this.schedulingAlgorithm) {
      case 'FCFS':
        this.fcfsSchedule();
        break;
      case 'SJF':
        this.sjfSchedule();
        break;
      case 'RR':
        this.roundRobinSchedule();
        break;
      case 'PRIORITY':
        this.prioritySchedule();
        break;
    }
  }

  /**
   * 先来先服务调度
   */
  private fcfsSchedule(): void {
    const readyProcesses = this.processes.filter(p => p.state === ProcessState.READY);
    if (readyProcesses.length > 0 && !this.currentRunningProcess) {
      this.runProcess(readyProcesses[0]);
    }
  }

  /**
   * 最短作业优先调度
   */
  private sjfSchedule(): void {
    const readyProcesses = this.processes
      .filter(p => p.state === ProcessState.READY)
      .sort((a, b) => a.burstTime - b.burstTime);
    
    if (readyProcesses.length > 0 && !this.currentRunningProcess) {
      this.runProcess(readyProcesses[0]);
    }
  }

  /**
   * 轮转调度
   */
  private roundRobinSchedule(): void {
    if (this.currentRunningProcess) {
      if (this.currentRunningProcess.cpuTime >= this.timeQuantum) {
        this.preemptProcess(this.currentRunningProcess);
      }
    }
    
    const readyProcesses = this.processes.filter(p => p.state === ProcessState.READY);
    if (readyProcesses.length > 0 && !this.currentRunningProcess) {
      this.runProcess(readyProcesses[0]);
    }
  }

  /**
   * 优先级调度
   */
  private prioritySchedule(): void {
    const readyProcesses = this.processes
      .filter(p => p.state === ProcessState.READY)
      .sort((a, b) => b.priority - a.priority);
    
    if (readyProcesses.length > 0) {
      if (this.currentRunningProcess && 
          readyProcesses[0].priority > this.currentRunningProcess.priority) {
        this.preemptProcess(this.currentRunningProcess);
      }
      
      if (!this.currentRunningProcess) {
        this.runProcess(readyProcesses[0]);
      }
    }
  }

  /**
   * 运行进程
   */
  private runProcess(process: Process): void {
    process.state = ProcessState.RUNNING;
    this.currentRunningProcess = process;
    
    // 动画：将进程移动到CPU
    if (process.mesh) {
      this.animateProcessToCPU(process.mesh);
    }
  }

  /**
   * 抢占进程
   */
  private preemptProcess(process: Process): void {
    process.state = ProcessState.READY;
    process.cpuTime = 0;
    this.currentRunningProcess = null;
    
    // 动画：将进程移回就绪队列
    if (process.mesh) {
      const readyIndex = this.processes.filter(p => p.state === ProcessState.READY).length;
      this.animateProcessToQueue(process.mesh, readyIndex);
    }
  }

  /**
   * 动画：将进程移动到CPU
   */
  private animateProcessToCPU(mesh: THREE.Mesh): void {
    const targetPosition = new THREE.Vector3(0, 8, 0);
    // 这里应该使用动画库如GSAP或Tween.js
    // 简化起见，直接设置位置
    mesh.position.copy(targetPosition);
  }

  /**
   * 动画：将进程移回队列
   */
  private animateProcessToQueue(mesh: THREE.Mesh, index: number): void {
    mesh.position.set(
      this.readyQueue.position.x,
      2,
      -8 + index * 4
    );
  }

  /**
   * 更新调度器状态
   */
  public update(deltaTime: number): void {
    this.clock += deltaTime;
    
    // 更新当前运行进程的CPU时间
    if (this.currentRunningProcess) {
      this.currentRunningProcess.cpuTime += deltaTime;
      
      // 检查进程是否完成
      if (this.currentRunningProcess.cpuTime >= this.currentRunningProcess.burstTime) {
        this.terminateProcess(this.currentRunningProcess);
      }
    }
    
    // 更新等待进程的等待时间
    this.processes
      .filter(p => p.state === ProcessState.READY || p.state === ProcessState.WAITING)
      .forEach(p => p.waitTime += deltaTime);
    
    // 旋转CPU核心
    if (this.cpuCore) {
      this.cpuCore.rotation.y += deltaTime * 0.5;
    }
    
    // 执行调度
    this.schedule();
  }

  /**
   * 终止进程
   */
  private terminateProcess(process: Process): void {
    process.state = ProcessState.TERMINATED;
    this.currentRunningProcess = null;
    
    if (process.mesh) {
      // 淡出动画
      const material = process.mesh.material as THREE.MeshPhongMaterial;
      material.transparent = true;
      material.opacity = 0.3;
    }
  }

  /**
   * 设置调度算法
   */
  public setSchedulingAlgorithm(algorithm: 'FCFS' | 'SJF' | 'RR' | 'PRIORITY'): void {
    this.schedulingAlgorithm = algorithm;
  }

  /**
   * 获取统计信息
   */
  public getStats(): any {
    const runningProcess = this.processes.find(p => p.state === ProcessState.RUNNING);
    const readyCount = this.processes.filter(p => p.state === ProcessState.READY).length;
    const waitingCount = this.processes.filter(p => p.state === ProcessState.WAITING).length;
    const terminatedCount = this.processes.filter(p => p.state === ProcessState.TERMINATED).length;
    
    return {
      algorithm: this.schedulingAlgorithm,
      currentProcess: runningProcess?.name || 'None',
      readyQueue: readyCount,
      waitingQueue: waitingCount,
      terminated: terminatedCount,
      clock: this.clock.toFixed(2)
    };
  }

  /**
   * 重置调度器
   */
  public reset(): void {
    this.processes.forEach(p => {
      p.state = ProcessState.READY;
      p.cpuTime = 0;
      p.waitTime = 0;
      
      if (p.mesh) {
        const material = p.mesh.material as THREE.MeshPhongMaterial;
        material.opacity = 1;
      }
    });
    
    this.currentRunningProcess = null;
    this.clock = 0;
    
    // 重新排列进程
    this.processes.forEach((p, i) => this.positionProcessInQueue(p, i));
  }
}

import './style.css';
import { SceneManager } from './core/SceneManager';
import { ProcessScheduler } from './modules/ProcessScheduler';
import { MemoryManager } from './modules/MemoryManager';

/**
 * OS-Verse 主应用类
 * 管理整个操作系统元宇宙的初始化和运行
 */
class OSVerse {
  private sceneManager: SceneManager;
  private processScheduler?: ProcessScheduler;
  private memoryManager?: MemoryManager;
  private currentMode: 'process' | 'memory' = 'process';
  private lastTime: number = 0;
  private statsElement: HTMLElement;

  constructor() {
    // 获取容器元素
    const container = document.getElementById('canvas-container')!;
    this.statsElement = document.getElementById('stats')!;
    
    // 初始化场景管理器
    this.sceneManager = new SceneManager(container);
    
    // 初始化UI控制
    this.initializeUI();
    
    // 默认启动进程调度模式
    this.switchToProcessMode();
    
    // 开始渲染循环
    this.sceneManager.startRenderLoop(() => this.update());
  }

  /**
   * 初始化用户界面控制
   */
  private initializeUI(): void {
    // 模式切换按钮
    const modeButtons = document.querySelectorAll('.mode-btn');
    modeButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const mode = target.getAttribute('data-mode');
        
        // 更新按钮样式
        modeButtons.forEach(b => b.classList.remove('active'));
        target.classList.add('active');
        
        // 切换模式
        if (mode === 'process') {
          this.switchToProcessMode();
        } else if (mode === 'memory') {
          this.switchToMemoryMode();
        }
      });
    });
    
    // 默认激活进程调度按钮
    document.querySelector('[data-mode="process"]')?.classList.add('active');
    
    // 添加控制按钮
    this.addControlButtons();
  }

  /**
   * 添加控制按钮
   */
  private addControlButtons(): void {
    const controlPanel = document.getElementById('control-panel')!;
    
    // 创建控制按钮容器
    const controls = document.createElement('div');
    controls.id = 'controls';
    controls.style.marginTop = '15px';
    
    // 进程调度控制
    const processControls = `
      <div id="process-controls" style="display: block;">
        <label style="color: #9ca3af; font-size: 12px;">调度算法：</label>
        <select id="scheduling-algorithm" style="background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.2); padding: 5px; border-radius: 5px; margin-bottom: 10px;">
          <option value="RR">轮转调度 (RR)</option>
          <option value="FCFS">先来先服务 (FCFS)</option>
          <option value="SJF">最短作业优先 (SJF)</option>
          <option value="PRIORITY">优先级调度</option>
        </select>
        <br>
        <button id="reset-scheduler" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-top: 10px;">重置调度器</button>
      </div>
    `;
    
    // 内存管理控制
    const memoryControls = `
      <div id="memory-controls" style="display: none;">
        <label style="color: #9ca3af; font-size: 12px;">分配算法：</label>
        <select id="allocation-algorithm" style="background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.2); padding: 5px; border-radius: 5px; margin-bottom: 10px;">
          <option value="FIRST_FIT">首次适应</option>
          <option value="BEST_FIT">最佳适应</option>
          <option value="WORST_FIT">最差适应</option>
        </select>
        <br>
        <button id="allocate-memory" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-right: 5px;">分配内存</button>
        <button id="free-memory" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-right: 5px;">释放内存</button>
        <button id="defragment" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: none; color: white; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-top: 10px;">内存整理</button>
      </div>
    `;
    
    controls.innerHTML = processControls + memoryControls;
    controlPanel.appendChild(controls);
    
    // 绑定事件
    this.bindControlEvents();
  }

  /**
   * 绑定控制事件
   */
  private bindControlEvents(): void {
    // 进程调度算法切换
    const schedulingSelect = document.getElementById('scheduling-algorithm') as HTMLSelectElement;
    schedulingSelect?.addEventListener('change', (e) => {
      const algorithm = (e.target as HTMLSelectElement).value as any;
      this.processScheduler?.setSchedulingAlgorithm(algorithm);
    });
    
    // 重置调度器
    document.getElementById('reset-scheduler')?.addEventListener('click', () => {
      this.processScheduler?.reset();
    });
    
    // 内存分配算法切换
    const allocationSelect = document.getElementById('allocation-algorithm') as HTMLSelectElement;
    allocationSelect?.addEventListener('change', (e) => {
      const algorithm = (e.target as HTMLSelectElement).value as any;
      this.memoryManager?.setAllocationAlgorithm(algorithm);
    });
    
    // 分配内存
    document.getElementById('allocate-memory')?.addEventListener('click', () => {
      const processes = ['Firefox', 'Photoshop', 'Game', 'Database', 'Compiler'];
      const randomProcess = processes[Math.floor(Math.random() * processes.length)];
      const size = Math.floor(Math.random() * 256) + 64;
      this.memoryManager?.allocateMemory(randomProcess, size);
    });
    
    // 释放内存
    document.getElementById('free-memory')?.addEventListener('click', () => {
      // 这里简化处理，实际应该有选择进程的UI
      const processes = ['Firefox', 'Photoshop', 'Game', 'Database', 'Compiler'];
      const randomProcess = processes[Math.floor(Math.random() * processes.length)];
      this.memoryManager?.freeMemory(randomProcess);
    });
    
    // 内存整理
    document.getElementById('defragment')?.addEventListener('click', () => {
      this.memoryManager?.defragment();
    });
  }

  /**
   * 切换到进程调度模式
   */
  private switchToProcessMode(): void {
    this.currentMode = 'process';
    
    // 清理场景
    this.sceneManager.clearScene();
    
    // 创建进程调度器
    this.processScheduler = new ProcessScheduler(this.sceneManager.getScene());
    this.memoryManager = undefined;
    
    // 显示进程控制
    const processControls = document.getElementById('process-controls');
    const memoryControls = document.getElementById('memory-controls');
    if (processControls) processControls.style.display = 'block';
    if (memoryControls) memoryControls.style.display = 'none';
  }

  /**
   * 切换到内存管理模式
   */
  private switchToMemoryMode(): void {
    this.currentMode = 'memory';
    
    // 清理场景
    this.sceneManager.clearScene();
    
    // 创建内存管理器
    this.memoryManager = new MemoryManager(this.sceneManager.getScene());
    this.processScheduler = undefined;
    
    // 显示内存控制
    const processControls = document.getElementById('process-controls');
    const memoryControls = document.getElementById('memory-controls');
    if (processControls) processControls.style.display = 'none';
    if (memoryControls) memoryControls.style.display = 'block';
  }

  /**
   * 更新循环
   */
  private update(): void {
    const currentTime = performance.now();
    const deltaTime = (currentTime - this.lastTime) / 1000;
    this.lastTime = currentTime;
    
    // 更新当前模块
    if (this.currentMode === 'process' && this.processScheduler) {
      this.processScheduler.update(deltaTime);
      this.updateProcessStats();
    } else if (this.currentMode === 'memory' && this.memoryManager) {
      this.memoryManager.update(deltaTime);
      this.updateMemoryStats();
    }
  }

  /**
   * 更新进程统计信息
   */
  private updateProcessStats(): void {
    if (!this.processScheduler) return;
    
    const stats = this.processScheduler.getStats();
    this.statsElement.innerHTML = `
      <div class="stat-item">
        <span class="stat-label">调度算法</span>
        <span class="stat-value">${stats.algorithm}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">当前进程</span>
        <span class="stat-value process-running">${stats.currentProcess}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">就绪队列</span>
        <span class="stat-value process-waiting">${stats.readyQueue}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">等待队列</span>
        <span class="stat-value process-blocked">${stats.waitingQueue}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已完成</span>
        <span class="stat-value">${stats.terminated}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">系统时钟</span>
        <span class="stat-value">${stats.clock}s</span>
      </div>
    `;
  }

  /**
   * 更新内存统计信息
   */
  private updateMemoryStats(): void {
    if (!this.memoryManager) return;
    
    const stats = this.memoryManager.getStats();
    this.statsElement.innerHTML = `
      <div class="stat-item">
        <span class="stat-label">分配算法</span>
        <span class="stat-value">${stats.algorithm}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">总内存</span>
        <span class="stat-value">${stats.totalMemory}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">空闲内存</span>
        <span class="stat-value memory-free">${stats.freeMemory}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已用内存</span>
        <span class="stat-value memory-used">${stats.usedMemory}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">系统内存</span>
        <span class="stat-value">${stats.systemMemory}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">交换区</span>
        <span class="stat-value memory-swap">${stats.swapUsed}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">碎片率</span>
        <span class="stat-value">${stats.fragmentation}</span>
      </div>
    `;
  }
}

// 启动应用
new OSVerse();

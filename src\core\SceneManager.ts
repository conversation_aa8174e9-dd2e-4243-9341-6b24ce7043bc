import * as THREE from 'three';

/**
 * 场景管理器类
 * 负责Three.js场景的初始化、渲染和基础设置
 */
export class SceneManager {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private animationId: number | null = null;

  constructor(container: HTMLElement) {
    this.scene = new THREE.Scene();
    
    // 初始化相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 15, 30);
    this.camera.lookAt(0, 0, 0);

    // 初始化渲染器
    this.renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: true 
    });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    
    container.appendChild(this.renderer.domElement);

    // 添加环境光和方向光
    this.setupLights();
    
    // 添加网格辅助线
    this.addGridHelper();
    
    // 处理窗口大小调整
    this.handleResize();

    // 添加雾效
    this.scene.fog = new THREE.Fog(0x0f0c29, 50, 200);
  }

  /**
   * 设置场景灯光
   */
  private setupLights(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
    this.scene.add(ambientLight);

    // 主方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(10, 20, 10);
    directionalLight.castShadow = true;
    directionalLight.shadow.camera.near = 0.1;
    directionalLight.shadow.camera.far = 100;
    directionalLight.shadow.camera.left = -30;
    directionalLight.shadow.camera.right = 30;
    directionalLight.shadow.camera.top = 30;
    directionalLight.shadow.camera.bottom = -30;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(directionalLight);

    // 点光源（模拟CPU核心光效）
    const pointLight = new THREE.PointLight(0x64b5f6, 1, 50);
    pointLight.position.set(0, 10, 0);
    this.scene.add(pointLight);
  }

  /**
   * 添加网格辅助线
   */
  private addGridHelper(): void {
    const gridHelper = new THREE.GridHelper(50, 50, 0x444444, 0x222222);
    this.scene.add(gridHelper);
  }

  /**
   * 处理窗口大小调整
   */
  private handleResize(): void {
    window.addEventListener('resize', () => {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    });
  }

  /**
   * 开始渲染循环
   */
  public startRenderLoop(onUpdate?: () => void): void {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate);
      
      if (onUpdate) {
        onUpdate();
      }
      
      this.renderer.render(this.scene, this.camera);
    };
    animate();
  }

  /**
   * 停止渲染循环
   */
  public stopRenderLoop(): void {
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 清理场景中的所有对象
   */
  public clearScene(): void {
    while(this.scene.children.length > 0) {
      const child = this.scene.children[0];
      if (child instanceof THREE.Mesh) {
        child.geometry.dispose();
        if (child.material instanceof THREE.Material) {
          child.material.dispose();
        }
      }
      this.scene.remove(child);
    }
    
    // 重新添加基础元素
    this.setupLights();
    this.addGridHelper();
  }

  /**
   * 获取场景对象
   */
  public getScene(): THREE.Scene {
    return this.scene;
  }

  /**
   * 获取相机对象
   */
  public getCamera(): THREE.PerspectiveCamera {
    return this.camera;
  }

  /**
   * 获取渲染器对象
   */
  public getRenderer(): THREE.WebGLRenderer {
    return this.renderer;
  }
}

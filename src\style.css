* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', system-ui, sans-serif;
  overflow: hidden;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
}

#app {
  width: 100vw;
  height: 100vh;
  position: relative;
}

#canvas-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

#ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

#control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(20, 20, 40, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(100, 100, 255, 0.3);
  border-radius: 15px;
  padding: 20px;
  min-width: 300px;
  pointer-events: all;
  color: #fff;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

#control-panel h2 {
  font-size: 1.5em;
  margin-bottom: 15px;
  color: #64b5f6;
  text-align: center;
  text-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
}

#mode-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.mode-btn {
  flex: 1;
  padding: 10px 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.mode-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.mode-btn.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 0 20px rgba(245, 87, 108, 0.5);
}

#stats {
  font-size: 14px;
  line-height: 1.8;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
  transition: background 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.stat-label {
  color: #9ca3af;
  font-weight: 500;
}

.stat-value {
  color: #60a5fa;
  font-weight: 600;
}

/* 进程状态颜色 */
.process-running {
  color: #4ade80;
}

.process-waiting {
  color: #facc15;
}

.process-blocked {
  color: #f87171;
}

/* 内存状态颜色 */
.memory-free {
  color: #4ade80;
}

.memory-used {
  color: #60a5fa;
}

.memory-swap {
  color: #facc15;
}

/* 加载动画 */
.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #64b5f6;
  font-size: 24px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 提示信息 */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 5px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
}

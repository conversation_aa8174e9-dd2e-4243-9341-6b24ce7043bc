import * as THREE from 'three';

/**
 * 内存块状态枚举
 */
export const MemoryBlockState = {
  FREE: 'FREE',       // 空闲
  ALLOCATED: 'ALLOCATED', // 已分配
  RESERVED: 'RESERVED',   // 系统保留
  SWAP: 'SWAP'           // 交换区
} as const;
export type MemoryBlockState = typeof MemoryBlockState[keyof typeof MemoryBlockState];

/**
 * 内存块接口
 */
export interface MemoryBlock {
  id: number;
  startAddress: number;
  size: number;
  state: MemoryBlockState;
  processId?: number;
  processName?: string;
  mesh?: THREE.Mesh;
  lastAccessTime: number;
}

/**
 * 内存管理器类
 * 实现3D内存空间管理可视化
 */
export class MemoryManager {
  private scene: THREE.Scene;
  private memoryBlocks: MemoryBlock[] = [];
  private memoryContainer: THREE.Group;
  private totalMemorySize: number = 1024; // MB
  private blockSize: number = 64; // MB
  private allocationAlgorithm: 'FIRST_FIT' | 'BEST_FIT' | 'WORST_FIT' = 'FIRST_FIT';
  private fragmentationLevel: number = 0;
  private clock: number = 0;

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.memoryContainer = this.createMemoryContainer();
    this.createSwapArea();
    this.initializeMemoryBlocks();
  }

  /**
   * 创建内存容器（内存城市）
   */
  private createMemoryContainer(): THREE.Group {
    const container = new THREE.Group();
    container.position.set(0, 0, 0);

    // 创建内存基座
    const baseGeometry = new THREE.BoxGeometry(30, 1, 30);
    const baseMaterial = new THREE.MeshPhongMaterial({
      color: 0x1a237e,
      emissive: 0x0d47a1,
      emissiveIntensity: 0.2
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = -0.5;
    base.receiveShadow = true;
    container.add(base);

    // 添加边框
    const edges = new THREE.EdgesGeometry(baseGeometry);
    const lineMaterial = new THREE.LineBasicMaterial({ color: 0x64b5f6 });
    const wireframe = new THREE.LineSegments(edges, lineMaterial);
    base.add(wireframe);

    this.scene.add(container);
    return container;
  }

  /**
   * 创建交换区
   */
  private createSwapArea(): THREE.Group {
    const swapGroup = new THREE.Group();
    swapGroup.position.set(0, 0, -20);

    // 创建交换区平台
    const platformGeometry = new THREE.BoxGeometry(25, 0.5, 8);
    const platformMaterial = new THREE.MeshPhongMaterial({
      color: 0xffa726,
      transparent: true,
      opacity: 0.5,
      emissive: 0xff6f00,
      emissiveIntensity: 0.1
    });
    const platform = new THREE.Mesh(platformGeometry, platformMaterial);
    platform.position.y = -0.25;
    swapGroup.add(platform);

    this.scene.add(swapGroup);
    return swapGroup;
  }

  /**
   * 初始化内存块
   */
  private initializeMemoryBlocks(): void {
    const numBlocks = Math.floor(this.totalMemorySize / this.blockSize);
    const gridSize = Math.ceil(Math.sqrt(numBlocks));
    
    for (let i = 0; i < numBlocks; i++) {
      const block: MemoryBlock = {
        id: i,
        startAddress: i * this.blockSize,
        size: this.blockSize,
        state: MemoryBlockState.FREE,
        lastAccessTime: 0
      };

      // 预分配一些系统内存
      if (i < 2) {
        block.state = MemoryBlockState.RESERVED;
        block.processName = 'System';
      }

      block.mesh = this.createMemoryBlockMesh(block);
      
      // 计算在网格中的位置
      const row = Math.floor(i / gridSize);
      const col = i % gridSize;
      const spacing = 3;
      
      block.mesh.position.set(
        (col - gridSize / 2) * spacing,
        1,
        (row - gridSize / 2) * spacing
      );
      
      this.memoryContainer.add(block.mesh);
      this.memoryBlocks.push(block);
    }
  }

  /**
   * 创建内存块的3D模型
   */
  private createMemoryBlockMesh(block: MemoryBlock): THREE.Mesh {
    const geometry = new THREE.BoxGeometry(2.5, 2, 2.5);
    
    // 根据状态设置颜色
    const colors = {
      [MemoryBlockState.FREE]: 0x4caf50,
      [MemoryBlockState.ALLOCATED]: 0x2196f3,
      [MemoryBlockState.RESERVED]: 0x9c27b0,
      [MemoryBlockState.SWAP]: 0xff9800
    };
    
    const material = new THREE.MeshPhongMaterial({
      color: colors[block.state],
      emissive: colors[block.state],
      emissiveIntensity: 0.1,
      transparent: true,
      opacity: block.state === MemoryBlockState.FREE ? 0.3 : 0.9
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    
    // 添加内存地址标签
    this.addMemoryLabel(mesh, block);
    
    return mesh;
  }

  /**
   * 添加内存标签
   */
  private addMemoryLabel(mesh: THREE.Mesh, block: MemoryBlock): void {
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 128;
    const context = canvas.getContext('2d')!;
    
    context.fillStyle = 'rgba(0, 0, 0, 0.8)';
    context.fillRect(0, 0, 256, 128);
    
    context.fillStyle = 'white';
    context.font = '20px Arial';
    context.fillText(`0x${block.startAddress.toString(16).toUpperCase()}`, 10, 30);
    
    if (block.processName) {
      context.font = '16px Arial';
      context.fillText(block.processName, 10, 60);
    }
    
    context.font = '14px Arial';
    context.fillText(`${block.size}MB`, 10, 90);
    
    const texture = new THREE.CanvasTexture(canvas);
    const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(3, 1.5, 1);
    sprite.position.y = 2;
    
    mesh.add(sprite);
  }

  /**
   * 分配内存
   */
  public allocateMemory(processName: string, size: number): boolean {
    const requiredBlocks = Math.ceil(size / this.blockSize);
    let allocatedBlocks: MemoryBlock[] = [];
    
    switch (this.allocationAlgorithm) {
      case 'FIRST_FIT':
        allocatedBlocks = this.firstFit(requiredBlocks);
        break;
      case 'BEST_FIT':
        allocatedBlocks = this.bestFit(requiredBlocks);
        break;
      case 'WORST_FIT':
        allocatedBlocks = this.worstFit(requiredBlocks);
        break;
    }
    
    if (allocatedBlocks.length === requiredBlocks) {
      // 分配成功
      allocatedBlocks.forEach(block => {
        block.state = MemoryBlockState.ALLOCATED;
        block.processName = processName;
        block.lastAccessTime = this.clock;
        this.updateBlockVisual(block);
      });
      
      this.calculateFragmentation();
      return true;
    }
    
    // 分配失败，尝试使用交换区
    return this.swapOut(processName, size);
  }

  /**
   * 首次适应算法
   */
  private firstFit(requiredBlocks: number): MemoryBlock[] {
    const freeBlocks: MemoryBlock[] = [];
    let consecutiveCount = 0;
    
    for (const block of this.memoryBlocks) {
      if (block.state === MemoryBlockState.FREE) {
        freeBlocks.push(block);
        consecutiveCount++;
        
        if (consecutiveCount === requiredBlocks) {
          return freeBlocks;
        }
      } else {
        freeBlocks.length = 0;
        consecutiveCount = 0;
      }
    }
    
    return [];
  }

  /**
   * 最佳适应算法
   */
  private bestFit(requiredBlocks: number): MemoryBlock[] {
    const freeSegments: MemoryBlock[][] = [];
    let currentSegment: MemoryBlock[] = [];
    
    // 找出所有空闲段
    for (const block of this.memoryBlocks) {
      if (block.state === MemoryBlockState.FREE) {
        currentSegment.push(block);
      } else if (currentSegment.length > 0) {
        if (currentSegment.length >= requiredBlocks) {
          freeSegments.push([...currentSegment]);
        }
        currentSegment = [];
      }
    }
    
    if (currentSegment.length >= requiredBlocks) {
      freeSegments.push(currentSegment);
    }
    
    // 找最小的合适段
    freeSegments.sort((a, b) => a.length - b.length);
    
    if (freeSegments.length > 0) {
      return freeSegments[0].slice(0, requiredBlocks);
    }
    
    return [];
  }

  /**
   * 最差适应算法
   */
  private worstFit(requiredBlocks: number): MemoryBlock[] {
    const freeSegments: MemoryBlock[][] = [];
    let currentSegment: MemoryBlock[] = [];
    
    // 找出所有空闲段
    for (const block of this.memoryBlocks) {
      if (block.state === MemoryBlockState.FREE) {
        currentSegment.push(block);
      } else if (currentSegment.length > 0) {
        if (currentSegment.length >= requiredBlocks) {
          freeSegments.push([...currentSegment]);
        }
        currentSegment = [];
      }
    }
    
    if (currentSegment.length >= requiredBlocks) {
      freeSegments.push(currentSegment);
    }
    
    // 找最大的段
    freeSegments.sort((a, b) => b.length - a.length);
    
    if (freeSegments.length > 0) {
      return freeSegments[0].slice(0, requiredBlocks);
    }
    
    return [];
  }

  /**
   * 交换出内存
   */
  private swapOut(processName: string, size: number): boolean {
    // 找到最久未使用的进程
    const allocatedBlocks = this.memoryBlocks.filter(b => 
      b.state === MemoryBlockState.ALLOCATED && b.processName !== 'System'
    );
    
    if (allocatedBlocks.length === 0) return false;
    
    allocatedBlocks.sort((a, b) => a.lastAccessTime - b.lastAccessTime);
    
    // 将最久未使用的进程移到交换区
    const victim = allocatedBlocks[0];
    if (victim.processName) {
      const swappedBlocks = this.memoryBlocks.filter(b => 
        b.processName === victim.processName
      );
      
      swappedBlocks.forEach(block => {
        block.state = MemoryBlockState.SWAP;
        this.updateBlockVisual(block);
        
        // 视觉效果：移动到交换区
        if (block.mesh) {
          block.mesh.position.z -= 15;
        }
      });
      
      // 现在重新尝试分配
      return this.allocateMemory(processName, size);
    }
    
    return false;
  }

  /**
   * 释放内存
   */
  public freeMemory(processName: string): void {
    const blocks = this.memoryBlocks.filter(b => b.processName === processName);
    
    blocks.forEach(block => {
      block.state = MemoryBlockState.FREE;
      block.processName = undefined;
      this.updateBlockVisual(block);
    });
    
    this.calculateFragmentation();
  }

  /**
   * 更新内存块视觉效果
   */
  private updateBlockVisual(block: MemoryBlock): void {
    if (!block.mesh) return;
    
    const colors = {
      [MemoryBlockState.FREE]: 0x4caf50,
      [MemoryBlockState.ALLOCATED]: 0x2196f3,
      [MemoryBlockState.RESERVED]: 0x9c27b0,
      [MemoryBlockState.SWAP]: 0xff9800
    };
    
    const material = block.mesh.material as THREE.MeshPhongMaterial;
    material.color.setHex(colors[block.state]);
    material.emissive.setHex(colors[block.state]);
    material.opacity = block.state === MemoryBlockState.FREE ? 0.3 : 0.9;
    
    // 更新标签
    block.mesh.children = [];
    this.addMemoryLabel(block.mesh, block);
    
    // 添加动画效果
    this.animateBlockChange(block.mesh);
  }

  /**
   * 内存块变化动画
   */
  private animateBlockChange(mesh: THREE.Mesh): void {
    // 简单的缩放动画
    const originalScale = mesh.scale.clone();
    mesh.scale.set(1.2, 1.2, 1.2);
    
    // 这里应该使用动画库，简化起见直接恢复
    setTimeout(() => {
      mesh.scale.copy(originalScale);
    }, 300);
  }

  /**
   * 计算内存碎片率
   */
  private calculateFragmentation(): void {
    let freeBlocks = 0;
    let largestFreeSegment = 0;
    let currentSegmentSize = 0;
    
    for (const block of this.memoryBlocks) {
      if (block.state === MemoryBlockState.FREE) {
        freeBlocks++;
        currentSegmentSize++;
      } else {
        if (currentSegmentSize > largestFreeSegment) {
          largestFreeSegment = currentSegmentSize;
        }
        currentSegmentSize = 0;
      }
    }
    
    if (currentSegmentSize > largestFreeSegment) {
      largestFreeSegment = currentSegmentSize;
    }
    
    if (freeBlocks > 0) {
      this.fragmentationLevel = 1 - (largestFreeSegment / freeBlocks);
    } else {
      this.fragmentationLevel = 0;
    }
  }

  /**
   * 内存整理（压缩）
   */
  public defragment(): void {
    const allocatedBlocks: MemoryBlock[] = [];
    const freeBlocks: MemoryBlock[] = [];
    
    // 分离已分配和空闲块
    this.memoryBlocks.forEach(block => {
      if (block.state === MemoryBlockState.ALLOCATED || 
          block.state === MemoryBlockState.RESERVED) {
        allocatedBlocks.push(block);
      } else if (block.state === MemoryBlockState.FREE) {
        freeBlocks.push(block);
      }
    });
    
    // 重新排列：先放已分配的，再放空闲的
    const reorganized = [...allocatedBlocks, ...freeBlocks];
    const gridSize = Math.ceil(Math.sqrt(reorganized.length));
    
    reorganized.forEach((block, i) => {
      const row = Math.floor(i / gridSize);
      const col = i % gridSize;
      const spacing = 3;
      
      if (block.mesh) {
        // 动画移动到新位置
        const newPosition = new THREE.Vector3(
          (col - gridSize / 2) * spacing,
          1,
          (row - gridSize / 2) * spacing
        );
        
        // 这里应该使用动画库
        block.mesh.position.copy(newPosition);
      }
    });
    
    this.calculateFragmentation();
  }

  /**
   * 更新内存管理器
   */
  public update(deltaTime: number): void {
    this.clock += deltaTime;
    
    // 内存块呼吸效果
    this.memoryBlocks.forEach(block => {
      if (block.mesh && block.state === MemoryBlockState.ALLOCATED) {
        const scale = 1 + Math.sin(this.clock * 2 + block.id) * 0.05;
        block.mesh.scale.y = scale;
      }
    });
    
    // 更新访问时间（模拟）
    if (Math.random() < 0.1) {
      const allocatedBlocks = this.memoryBlocks.filter(b => 
        b.state === MemoryBlockState.ALLOCATED
      );
      if (allocatedBlocks.length > 0) {
        const randomBlock = allocatedBlocks[Math.floor(Math.random() * allocatedBlocks.length)];
        randomBlock.lastAccessTime = this.clock;
        
        // 访问时的视觉效果
        if (randomBlock.mesh) {
          const material = randomBlock.mesh.material as THREE.MeshPhongMaterial;
          material.emissiveIntensity = 0.5;
          setTimeout(() => {
            material.emissiveIntensity = 0.1;
          }, 200);
        }
      }
    }
  }

  /**
   * 获取内存统计信息
   */
  public getStats(): any {
    const freeBlocks = this.memoryBlocks.filter(b => b.state === MemoryBlockState.FREE).length;
    const allocatedBlocks = this.memoryBlocks.filter(b => b.state === MemoryBlockState.ALLOCATED).length;
    const reservedBlocks = this.memoryBlocks.filter(b => b.state === MemoryBlockState.RESERVED).length;
    const swappedBlocks = this.memoryBlocks.filter(b => b.state === MemoryBlockState.SWAP).length;
    
    return {
      totalMemory: `${this.totalMemorySize} MB`,
      freeMemory: `${freeBlocks * this.blockSize} MB`,
      usedMemory: `${allocatedBlocks * this.blockSize} MB`,
      systemMemory: `${reservedBlocks * this.blockSize} MB`,
      swapUsed: `${swappedBlocks * this.blockSize} MB`,
      fragmentation: `${(this.fragmentationLevel * 100).toFixed(1)}%`,
      algorithm: this.allocationAlgorithm
    };
  }

  /**
   * 设置分配算法
   */
  public setAllocationAlgorithm(algorithm: 'FIRST_FIT' | 'BEST_FIT' | 'WORST_FIT'): void {
    this.allocationAlgorithm = algorithm;
  }

  /**
   * 重置内存
   */
  public reset(): void {
    this.memoryBlocks.forEach((block, i) => {
      if (i < 2) {
        block.state = MemoryBlockState.RESERVED;
        block.processName = 'System';
      } else {
        block.state = MemoryBlockState.FREE;
        block.processName = undefined;
      }
      block.lastAccessTime = 0;
      this.updateBlockVisual(block);
    });
    
    this.fragmentationLevel = 0;
    this.clock = 0;
  }
}

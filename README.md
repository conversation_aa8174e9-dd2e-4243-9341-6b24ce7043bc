简体中文 | [English](README-EN.md)

# OS-Verse — 操作系统元宇宙 (Three.js 可视化)

一个使用 Three.js + Vite + TypeScript 构建的 3D 教学演示项目，用可视化的方式展示操作系统中的两个核心主题：
- 进程调度（FCFS / SJF / RR / Priority）
- 内存管理（首次/最佳/最差 适应 + 交换区 + 碎片率 + 内存整理）

项目以“元宇宙城市”的形式呈现：CPU 核心、就绪/等待队列、内存城市与交换区都以 3D 几何体与动态效果展示，配合左侧控制面板进行交互。


## 快速开始

前置条件：
- Node.js 18+（推荐 20+）
- 包管理器：推荐 pnpm，也可使用 npm/yarn

克隆并启动开发服务器：

```bash
# 安装依赖（任选其一）
pnpm install
# 或
npm install
# 或
yarn install

# 启动开发服务器（Vite）
pnpm dev
# 或
npm run dev
# 或
yarn dev
```

打开浏览器访问（Vite 默认端口）：
- http://localhost:5173

构建生产版本：
```bash
pnpm build
# 或 npm run build / yarn build
```

本地预览生产构建：
```bash
pnpm preview
# 或 npm run preview / yarn preview
```


## 功能与交互

页面左上角为控制面板，可在两种模式之间切换：

### 1) 进程调度模式
- 调度算法选择：
  - RR（轮转，默认，时间片 timeQuantum=2s）
  - FCFS（先来先服务）
  - SJF（最短作业优先）
  - PRIORITY（优先级，高优先级可抢占）
- 重置调度器：恢复初始进程状态与时钟。
- 可视化要点：
  - CPU 为发光立方体并缓慢旋转。
  - 进程以彩色球体表示，颜色代表优先级，携带名称标签（如 Chrome/VSCode 等）。
  - 进程在就绪队列、CPU 与（可扩展的）等待区之间移动与状态切换。
  - 完成的进程会淡出显示。
- 统计信息（实时）：
  - 调度算法、当前进程、就绪/等待队列长度、已完成进程数、系统时钟。

### 2) 内存管理模式
- 分配算法选择：
  - FIRST_FIT（首次适应）
  - BEST_FIT（最佳适应）
  - WORST_FIT（最差适应）
- 操作按钮：
  - 分配内存：演示按钮会随机选择一个进程名并申请 64~320MB。
  - 释放内存：演示按钮会随机释放一个进程的内存。
  - 内存整理（Defragment）：对块进行重新布局以降低碎片率（视觉上重新排列网格）。
- 可视化要点：
  - “内存城市”以网格小方块（块大小 64MB）呈现，前两块保留给系统（RESERVED）。
  - 块颜色与透明度表示状态（FREE/ALLOCATED/RESERVED/SWAP），标签展示起始地址（十六进制）与大小。
  - 若分配失败，尝试将“最久未使用”的进程块换出到交换区（SWAP），并再次尝试分配。
  - 已分配块具有轻微“呼吸”动画，被访问时短暂提升发光强度。
- 统计信息（实时）：
  - 总内存、空闲/已用/系统内存、交换区占用、碎片率、当前分配算法。


## 技术栈
- Three.js（0.179.x）：3D 场景、灯光、材质与几何体
- Vite（7.x）：快速开发与构建
- TypeScript（严格模式）：类型安全与现代语法


## 项目结构

```
.
├─ index.html                 # 页面容器与 UI
├─ src/
│  ├─ main.ts                 # 应用入口，模式切换与 UI 绑定
│  ├─ style.css               # UI/样式
│  ├─ core/
│  │  └─ SceneManager.ts      # Three 场景/相机/渲染器与基础灯光、网格
│  └─ modules/
│     ├─ ProcessScheduler.ts  # 进程调度可视化（FCFS/SJF/RR/Priority）
│     └─ MemoryManager.ts     # 内存管理可视化（First/Best/Worst Fit, Swap, Defrag）
```

主要类与职责：
- SceneManager
  - 初始化 Three 场景、相机、渲染器、灯光与网格，封装渲染循环与自适应。
- ProcessScheduler
  - 管理进程实体（名称、优先级、burstTime、状态等），在 CPU/队列间切换。
  - 实现 FCFS/SJF/RR/PRIORITY 调度策略与基本可视化动画。
- MemoryManager
  - 管理内存块（起始地址、大小、状态、最近访问时间等）。
  - 实现 First/Best/Worst Fit 分配、LRU 风格的 Swap-Out、碎片率计算与整理。


## 可配置参数（开发者）
- 进程调度：`src/modules/ProcessScheduler.ts`
  - `timeQuantum`（默认 2 秒，RR 时间片）
  - 初始进程名称与优先级（`initializeProcesses` 内）
- 内存管理：`src/modules/MemoryManager.ts`
  - `totalMemorySize`（默认 1024 MB）
  - `blockSize`（默认 64 MB）
  - 初始保留块数量与标签（前两块为系统保留）


## 常见问题（FAQ）
- 启动后白屏或渲染异常？
  - 请确认浏览器支持 WebGL / WebGL2，或在桌面 Chrome/Edge/Firefox 最新版运行。
  - 控制台是否有错误日志（F12 -> Console）？若有 TypeScript/Vite 报错，请反馈具体信息。
- 如何部署到静态托管（如 GitHub Pages/Netlify/Vercel）？
  - 运行 `pnpm build` 后将 `dist/` 目录部署到静态站点即可。


## 开源许可
本项目使用 MIT 许可证发布。详见 [LICENSE](LICENSE)。


## 致谢
- Three.js 社区与文档
- Vite/TypeScript 生态
[简体中文](README.md) | English

# OS-Verse — Operating System Metaverse (Three.js Visualization)

A 3D educational demo built with Three.js + Vite + TypeScript that visualizes two fundamental OS topics:
- Process Scheduling (FCFS / SJF / RR / Priority)
- Memory Management (First/Best/Worst Fit + Swap + Fragmentation + Defragmentation)

The project presents a "metaverse city" where the CPU core, ready/waiting queues, memory city, and swap area are represented with 3D geometry and dynamic effects, controlled via a panel on the left.


## Quick Start

Prerequisites:
- Node.js 18+ (20+ recommended)
- Package manager: pnpm recommended (npm/yarn also work)

Clone and start the dev server:

```bash
# Install dependencies (pick one)
pnpm install
# or
npm install
# or
yarn install

# Start the dev server (Vite)
pnpm dev
# or
npm run dev
# or
yarn dev
```

Open in your browser (Vite default port):
- http://localhost:5173

Build for production:
```bash
pnpm build
# or npm run build / yarn build
```

Preview the production build locally:
```bash
pnpm preview
# or npm run preview / yarn preview
```


## Features & Interactions

Use the control panel at the top-left to switch between two modes:

### 1) Process Scheduling Mode
- Algorithm options:
  - RR (Round Robin, default, time slice timeQuantum = 2s)
  - FCFS (First-Come First-Served)
  - SJF (Shortest Job First)
  - PRIORITY (preemptive: higher priority can preempt)
- Reset Scheduler: restore initial process states and clock.
- Visualization highlights:
  - CPU is a glowing rotating cube.
  - Processes are colored spheres; color encodes priority and a label shows the name (e.g., Chrome/VSCode).
  - Processes move between the ready queue, CPU, and a waiting area (extensible) as states change.
  - Completed processes fade out.
- Live stats:
  - Scheduling algorithm, current process, ready/wait queue lengths, completed count, system clock.

### 2) Memory Management Mode
- Allocation algorithms:
  - FIRST_FIT
  - BEST_FIT
  - WORST_FIT
- Actions:
  - Allocate Memory: demo button randomly picks a process name and requests 64–320MB.
  - Free Memory: demo button randomly frees one process's memory.
  - Defragment: rearranges blocks to reduce fragmentation (visually reorders the grid).
- Visualization highlights:
  - "Memory city" is a grid of blocks (64MB each), with the first two blocks reserved for the system (RESERVED).
  - Block color/opacity indicates state (FREE/ALLOCATED/RESERVED/SWAP); labels show start address (hex) and size.
  - If allocation fails, attempts swap-out of the least-recently-used process blocks (SWAP), then retries allocation.
  - Allocated blocks have a subtle "breathing" animation; on access, emissive intensity briefly increases.
- Live stats:
  - Total, free, used, and system memory; swap usage; fragmentation; current allocation algorithm.


## Tech Stack
- Three.js (0.179.x): 3D scene, lights, materials, geometry
- Vite (7.x): fast dev/build
- TypeScript (strict): types and modern syntax


## Project Structure

```
.
├─ index.html                 # Page container and UI
├─ src/
│  ├─ main.ts                 # App entry; mode switching and UI bindings
│  ├─ style.css               # UI/styles
│  ├─ core/
│  │  └─ SceneManager.ts      # Three scene/camera/renderer; lights, grid, render loop
│  └─ modules/
│     ├─ ProcessScheduler.ts  # Process scheduling visualization (FCFS/SJF/RR/Priority)
│     └─ MemoryManager.ts     # Memory management visualization (First/Best/Worst Fit, Swap, Defrag)
```

Key classes and responsibilities:
- SceneManager
  - Initialize Three scene, camera, renderer, lights, grid; provide render loop and resize handling.
- ProcessScheduler
  - Manage process entities (name, priority, burstTime, state, etc.); switch between CPU and queues.
  - Implement FCFS/SJF/RR/PRIORITY scheduling with basic animations.
- MemoryManager
  - Manage memory blocks (startAddress, size, state, lastAccessTime, etc.).
  - Implement First/Best/Worst Fit allocation, LRU-like swap-out, fragmentation calculation, and defragmentation.


## Configurable Parameters (for developers)
- Process scheduling: `src/modules/ProcessScheduler.ts`
  - `timeQuantum` (default 2s, RR time slice)
  - Initial process names and priorities (inside `initializeProcesses`)
- Memory management: `src/modules/MemoryManager.ts`
  - `totalMemorySize` (default 1024 MB)
  - `blockSize` (default 64 MB)
  - Number/labels of reserved blocks (first two are system-reserved)


## FAQ
- Blank screen or render issues?
  - Ensure your browser supports WebGL/WebGL2; use the latest desktop Chrome/Edge/Firefox.
  - Check the console (F12 -> Console) for errors. If TypeScript/Vite errors occur, please share the details.
- How to deploy to a static host (GitHub Pages/Netlify/Vercel)?
  - Run `pnpm build` and deploy the `dist/` directory.


## License
This project is licensed under the MIT License. See [LICENSE](LICENSE) for details.


## Acknowledgements
- Three.js community and docs
- Vite/TypeScript ecosystem
